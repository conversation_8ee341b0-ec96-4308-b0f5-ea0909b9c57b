<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="theme-color" content="#1a365d">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>من هو الأستاذ فراس رحيم مجيسر - رجل أعمال وإنساني ملتزم</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .about-hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: var(--light-color);
            padding: 140px 20px 80px;
            text-align: center;
        }

        .bio-section {
            padding: 80px 20px;
        }

        .bio-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4rem;
            align-items: start;
            max-width: 1200px;
            margin: 0 auto;
        }

        .bio-image {
            text-align: center;
        }

        .bio-image img {
            width: 100%;
            max-width: 350px;
            border-radius: 20px;
            box-shadow: var(--shadow);
        }

        .bio-text {
            font-size: 1.1rem;
            line-height: 1.8;
        }

        .bio-text h2 {
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            font-size: 2rem;
        }

        .bio-text p {
            margin-bottom: 1.5rem;
            color: var(--text-light);
        }

        .qualities {
            background: var(--background-light);
            padding: 80px 20px;
        }

        .qualities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .quality-card {
            background: var(--light-color);
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: var(--transition);
        }

        .quality-card:hover {
            transform: translateY(-10px);
        }

        .quality-card i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .quality-card h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .timeline {
            padding: 80px 20px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            position: relative;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23e2e8f0" opacity="0.3"/><circle cx="75" cy="75" r="1" fill="%23cbd5e0" opacity="0.2"/><circle cx="50" cy="10" r="0.5" fill="%23a0aec0" opacity="0.4"/><circle cx="10" cy="60" r="0.5" fill="%23718096" opacity="0.3"/><circle cx="90" cy="40" r="0.5" fill="%23e2e8f0" opacity="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            opacity: 0.6;
        }

        .timeline-container {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        /* تحسين الرؤية والوضوح */
        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 2rem;
        }
        
        .timeline-item:nth-child(even) .timeline-content {
            margin-right: 2rem;
        }

        .timeline-container::before {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, var(--secondary-color), var(--primary-color));
            border-radius: 2px;
        }

        .timeline-item {
            margin-bottom: 4rem;
            position: relative;
            width: 50%;
            opacity: 0;
            animation: fadeInTimeline 0.8s ease forwards;
        }

        .timeline-item:nth-child(1) { animation-delay: 0.2s; }
        .timeline-item:nth-child(2) { animation-delay: 0.4s; }
        .timeline-item:nth-child(3) { animation-delay: 0.6s; }
        .timeline-item:nth-child(4) { animation-delay: 0.8s; }
        .timeline-item:nth-child(5) { animation-delay: 1.0s; }

        @keyframes fadeInTimeline {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 4rem;
            text-align: right;
        }

        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 4rem;
            text-align: left;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            width: 24px;
            height: 24px;
            background: var(--secondary-color);
            border: 4px solid var(--light-color);
            border-radius: 50%;
            top: 20px;
            box-shadow: 0 0 0 4px rgba(56, 161, 105, 0.2);
            transition: all 0.3s ease;
        }

        .timeline-item:hover::before {
            transform: scale(1.2);
            box-shadow: 0 0 0 8px rgba(56, 161, 105, 0.3);
        }

        .timeline-item:nth-child(odd)::before {
            right: -16px;
        }

        .timeline-item:nth-child(even)::before {
            left: -16px;
        }

        .timeline-content {
            background: var(--light-color);
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(56, 161, 105, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .timeline-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .timeline-year {
            color: var(--secondary-color);
            font-weight: 700;
            font-size: 1.4rem;
            margin-bottom: 1rem;
            display: inline-block;
            background: rgba(56, 161, 105, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            border: 2px solid var(--secondary-color);
        }

        .timeline-title {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 1.2rem;
            font-weight: 600;
            line-height: 1.4;
        }

        .timeline-content p {
            color: var(--text-light);
            font-size: 1.1rem;
            line-height: 1.7;
            margin: 0;
        }

        /* قسم المعلومات السياسية */
        .political-info {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 80px 20px;
            position: relative;
            overflow: hidden;
        }

        .political-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="political-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%2338a169" opacity="0.1"/><circle cx="40" cy="40" r="1" fill="%231a365d" opacity="0.1"/><circle cx="25" cy="5" r="0.5" fill="%2338a169" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23political-pattern)"/></svg>');
            pointer-events: none;
            opacity: 0.4;
        }

        .political-content {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .political-card {
            background: var(--light-color);
            border-radius: 25px;
            padding: 3rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(56, 161, 105, 0.2);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .political-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 45px rgba(0, 0, 0, 0.15);
        }

        .political-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
        }

        .political-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .political-header h2 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
            position: relative;
            display: inline-block;
        }

        .political-header h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
            border-radius: 2px;
        }

        .political-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }

        .candidate-number {
            text-align: center;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            color: var(--light-color);
            border-radius: 20px;
            padding: 2rem;
            position: relative;
        }

        .candidate-number::before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border-radius: 25px;
            z-index: -1;
            opacity: 0.3;
        }

        .number-display {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .number-label {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .coalition-info {
            text-align: right;
        }

        .coalition-name {
            color: var(--primary-color);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            line-height: 1.3;
        }

        .coalition-leader {
            background: rgba(56, 161, 105, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            border-right: 4px solid var(--secondary-color);
            margin-bottom: 1.5rem;
        }

        .coalition-leader h4 {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 0.8rem;
            font-weight: 600;
        }

        .leader-name {
            color: var(--secondary-color);
            font-size: 1.4rem;
            font-weight: 700;
        }

        .coalition-description {
            color: var(--text-light);
            font-size: 1.1rem;
            line-height: 1.7;
        }

        @media (max-width: 768px) {
            .political-info {
                padding: 60px 15px;
            }

            .political-card {
                padding: 2rem;
            }

            .political-header h2 {
                font-size: 2rem;
            }

            .political-details {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .coalition-info {
                text-align: center;
            }

            .coalition-name {
                font-size: 1.7rem;
            }

            .number-display {
                font-size: 3rem;
            }
        }

        @media (max-width: 480px) {
            .political-info {
                padding: 40px 10px;
            }

            .political-card {
                padding: 1.5rem;
            }

            .political-header h2 {
                font-size: 1.8rem;
            }

            .coalition-name {
                font-size: 1.5rem;
            }

            .number-display {
                font-size: 2.5rem;
            }

            .coalition-leader {
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .about-hero {
                padding: 120px 15px 60px;
            }
            
            .about-hero h1 {
                font-size: 2rem;
                line-height: 1.2;
            }
            
            .about-hero p {
                font-size: 1.1rem;
            }
            
            .bio-section {
                padding: 60px 15px;
            }
            
            .bio-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }
            
            .bio-image img {
                max-width: 280px;
            }
            
            .bio-text h2 {
                font-size: 1.8rem;
            }
            
            .bio-text {
                font-size: 1rem;
                text-align: right;
            }
            
            .qualities {
                padding: 60px 15px;
            }
            
            .qualities h2 {
                font-size: 2rem !important;
                margin-bottom: 2rem !important;
            }
            
            .qualities-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .quality-card {
                padding: 1.5rem;
            }
            
            .quality-card i {
                font-size: 2.5rem;
            }
            
            .timeline {
                padding: 60px 15px;
            }
            
            .timeline h2 {
                font-size: 2.2rem !important;
                margin-bottom: 1rem !important;
            }
            
            .timeline p {
                font-size: 1.1rem !important;
                margin-top: 1rem !important;
            }
            
            .timeline .container > div:first-child {
                margin-bottom: 4rem !important;
            }

            .timeline-container::before {
                left: 30px;
                width: 3px;
            }

            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 4rem !important;
                padding-right: 1rem !important;
                margin-bottom: 3rem;
                text-align: right !important;
            }

            .timeline-item::before {
                left: 18px !important;
                width: 20px;
                height: 20px;
            }
            
            .timeline-content {
                padding: 2rem;
                margin-right: 0 !important;
                margin-left: 0 !important;
            }
            
            .timeline-year {
                font-size: 1.2rem;
                padding: 0.4rem 0.8rem;
            }
            
            .timeline-title {
                font-size: 1.3rem;
            }
            
            .timeline-content p {
                font-size: 1rem;
            }
        }
        
        @media (max-width: 480px) {
            .about-hero {
                padding: 100px 10px 40px;
            }
            
            .about-hero h1 {
                font-size: 1.8rem;
            }
            
            .about-hero p {
                font-size: 1rem;
            }
            
            .bio-section, .qualities, .timeline {
                padding: 40px 10px;
            }
            
            .bio-image img {
                max-width: 250px;
            }
            
            .bio-text h2 {
                font-size: 1.6rem;
            }
            
            .qualities h2, .timeline h2 {
                font-size: 2rem !important;
            }
            
            .timeline p {
                font-size: 1rem !important;
            }
            
            .timeline .container > div:first-child {
                margin-bottom: 3rem !important;
            }
            
            .quality-card {
                padding: 1.2rem;
            }
            
            .quality-card i {
                font-size: 2rem;
            }
            
            .quality-card h3 {
                font-size: 1.2rem;
            }
            
            .timeline-container::before {
                left: 25px;
                width: 2px;
            }
            
            .timeline-item {
                padding-left: 3.5rem !important;
                padding-right: 0.5rem !important;
            }
            
            .timeline-item::before {
                left: 15px !important;
                width: 18px;
                height: 18px;
            }
            
            .timeline-content {
                padding: 1.5rem;
                margin-right: 0 !important;
                margin-left: 0 !important;
            }
            
            .timeline-year {
                font-size: 1.1rem;
                padding: 0.3rem 0.6rem;
            }
            
            .timeline-title {
                font-size: 1.2rem;
            }
            
            .timeline-content p {
                font-size: 0.95rem;
                line-height: 1.6;
            }
        }
        
        @media (max-width: 360px) {
            .about-hero h1 {
                font-size: 1.6rem;
            }
            
            .bio-text h2 {
                font-size: 1.4rem;
            }
            
            .qualities h2, .timeline h2 {
                font-size: 1.8rem !important;
            }
            
            .timeline p {
                font-size: 0.9rem !important;
            }
            
            .timeline .container > div:first-child {
                margin-bottom: 2.5rem !important;
            }
            
            .quality-card i {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h2>الأستاذ فراس رحيم مجيسر</h2>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html">الرئيسية</a></li>
                    <li><a href="about.html" class="active">من هو الأستاذ فراس</a></li>
                    <li><a href="program.html">البرنامج الانتخابي</a></li>
                    <li><a href="achievements.html">الإنجازات</a></li>
                    <li><a href="gallery.html">معرض الصور</a></li>
                    <li><a href="join.html">انضم للحملة</a></li>
                    <li><a href="contact.html">اتصل بنا</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- قسم البطل -->
    <section class="about-hero">
        <div class="container">
            <h1>من هو الأستاذ فراس رحيم مجيسر؟</h1>
            <p>رجل أعمال ناجح، إنساني ملتزم، ومرشح مستقل يسعى لخدمة أهل بغداد</p>
        </div>
    </section>

    <!-- قسم السيرة الذاتية -->
    <section class="bio-section">
        <div class="container">
            <div class="bio-content">
                <div class="bio-image">
                    <img src="images/myfoto.jpg" alt="الأستاذ فراس رحيم مجيسر">
                </div>
                <div class="bio-text">
                    <h2>نشأة وتكوين في خدمة المجتمع</h2>
                    <p>
                        وُلد الأستاذ فراس رحيم مجيسر ونشأ في بغداد الحبيبة، حيث تشرّب منذ صغره قيم العطاء والإنسانية. 
                        حصل على تعليمه الجامعي في إدارة الأعمال، وسرعان ما برز كرائد أعمال مُبدع ومؤثر في المجتمع العراقي.
                    </p>
                    <p>
                        بدأ مسيرته المهنية من الصفر، وبفضل عزيمته وإيمانه بقدرات الشعب العراقي، نجح في تأسيس وإدارة 
                        عدة شركات ومؤسسات في مجالات متنوعة، مما جعله أحد أبرز رجال الأعمال المؤثرين في بغداد.
                    </p>
                    <p>
                        ما يميز الأستاذ فراس ليس فقط نجاحه في الأعمال، بل التزامه العميق بالعمل الخيري والإنساني. 
                        فقد كرّس جزءًا كبيرًا من وقته وموارده لدعم الفقراء والمحتاجين، وإطلاق مبادرات تنموية تساهم في 
                        تحسين أوضاع المجتمع العراقي.
                    </p>
                    <p>
                        اليوم، يخطو الأستاذ فراس خطوة جديدة في مسيرة العطاء، حيث يترشح للانتخابات كمرشح مستقل، 
                        حاملاً معه خبرة سنوات طويلة في إدارة الأعمال والعمل الاجتماعي، وعازمًا على تطبيق هذه الخبرة 
                        في خدمة أهل بغداد وتحقيق التنمية المستدامة.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم المعلومات السياسية والانتخابية -->
    <section class="political-info">
        <div class="container">
            <div class="political-content">
                <div class="political-card">
                    <div class="political-header">
                        <h2>المعلومات الانتخابية والسياسية</h2>
                    </div>
                    <div class="political-details">
                        <div class="candidate-number">
                            <div class="number-display">7</div>
                            <div class="number-label">التسلسل الانتخابي</div>
                            <p style="margin-top: 1rem; font-size: 1rem; opacity: 0.9;">رقم المرشح في القائمة الانتخابية</p>
                        </div>
                        <div class="coalition-info">
                            <h3 class="coalition-name">كتلة الإعمار والتنمية</h3>
                            <div class="coalition-leader">
                                <h4>قائد الكتلة:</h4>
                                <div class="leader-name">محمد شياع السوداني</div>
                                <p style="margin-top: 0.5rem; font-size: 0.95rem; color: var(--text-light);">رئيس مجلس الوزراء</p>
                            </div>
                            <p class="coalition-description">
                                كتلة سياسية تهدف إلى إعادة إعمار العراق وتحقيق التنمية المستدامة في جميع القطاعات، 
                                مع التركيز على تطوير البنية التحتية وتحسين الخدمات العامة وخلق فرص العمل للشباب العراقي.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم الصفات والمؤهلات -->
    <section class="qualities">
        <div class="container">
            <h2 style="text-align: center; font-size: 2.5rem; color: var(--primary-color); margin-bottom: 3rem;">
                المؤهلات والصفات القيادية
            </h2>
            <div class="qualities-grid">
                <div class="quality-card">
                    <i class="fas fa-lightbulb"></i>
                    <h3>الرؤية الاستراتيجية</h3>
                    <p>خبرة في وضع الخطط طويلة المدى وتحقيق الأهداف الطموحة</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-handshake"></i>
                    <h3>القيادة والإدارة</h3>
                    <p>قدرة مُثبتة على قيادة الفرق وإدارة المؤسسات بكفاءة عالية</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-heart"></i>
                    <h3>الالتزام الإنساني</h3>
                    <p>تاريخ حافل في العمل الخيري ودعم الفئات الأكثر احتياجًا</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-balance-scale"></i>
                    <h3>النزاهة والشفافية</h3>
                    <p>سمعة طيبة في الأمانة والصدق والتعامل الأخلاقي</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-users"></i>
                    <h3>التواصل مع الناس</h3>
                    <p>قدرة على فهم احتياجات المواطنين والتفاعل الإيجابي معهم</p>
                </div>
                <div class="quality-card">
                    <i class="fas fa-chart-line"></i>
                    <h3>خبرة اقتصادية</h3>
                    <p>فهم عميق للاقتصاد وآليات التنمية المستدامة</p>
                </div>
            </div>
        </div>
    </section>

    <!-- الخط الزمني -->
    <section class="timeline">
        <div class="container">
            <div style="text-align: center; margin-bottom: 5rem;">
                <h2 style="font-size: 2.8rem; color: var(--primary-color); margin-bottom: 1rem; font-weight: 700; position: relative; display: inline-block;">
                    مسيرة العطاء والإنجاز
                    <span style="position: absolute; bottom: -10px; left: 50%; transform: translateX(-50%); width: 80px; height: 4px; background: linear-gradient(90deg, var(--secondary-color), var(--primary-color)); border-radius: 2px;"></span>
                </h2>
                <p style="font-size: 1.2rem; color: var(--text-light); margin-top: 1.5rem; max-width: 600px; margin-left: auto; margin-right: auto; line-height: 1.6;">
                    رحلة حافلة بالإنجازات والعطاء المستمر في خدمة المجتمع العراقي
                </p>
            </div>
            <div class="timeline-container">
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-year">2010-2015</div>
                        <h3 class="timeline-title">بداية رحلة ريادة الأعمال</h3>
                        <p>تأسيس أول الشركات التجارية وبناء قاعدة صلبة في السوق العراقي</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-year">2016-2018</div>
                        <h3 class="timeline-title">التوسع والنمو</h3>
                        <p>إطلاق مؤسسات جديدة في مجالات مختلفة وتوفير فرص عمل للمئات</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-year">2019-2021</div>
                        <h3 class="timeline-title">بداية العمل الخيري المنظم</h3>
                        <p>إطلاق مبادرات إنسانية واسعة لدعم الأيتام والعوائل المتعففة</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-year">2022-2024</div>
                        <h3 class="timeline-title">التأثير المجتمعي الواسع</h3>
                        <p>دعم أكثر من 500 عائلة وإطلاق 25+ مشروع تنموي في بغداد</p>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-content">
                        <div class="timeline-year">2025</div>
                        <h3 class="timeline-title">خطوة نحو الخدمة العامة</h3>
                        <p>الترشح للانتخابات لتطبيق الخبرة العملية في خدمة أهل بغداد</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>الأستاذ فراس رحيم مجيسر</h3>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                    <div class="social-links">
                        <a href="https://www.facebook.com/share/1N5yyHKSvV/" target="_blank" rel="noopener noreferrer"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-telegram"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="https://www.tiktok.com/@firasmjiser" target="_blank" rel="noopener noreferrer"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="about.html">من هو الأستاذ فراس</a></li>
                        <li><a href="program.html">البرنامج الانتخابي</a></li>
                        <li><a href="achievements.html">الإنجازات</a></li>
                        <li><a href="join.html">انضم للحملة</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +964 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> بغداد، العراق</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 الأستاذ فراس رحيم مجيسر. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>
