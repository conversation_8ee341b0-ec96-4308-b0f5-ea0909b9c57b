<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="theme-color" content="#1a365d">
    <title>اختبار الاستجابة للهواتف - موقع الأستاذ فراس</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #f7fafc;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1a365d;
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2rem;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f7fafc;
            border-radius: 10px;
            border-right: 4px solid #38a169;
        }
        
        .test-section h2 {
            color: #38a169;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .test-label {
            font-weight: 600;
            color: #2d3748;
        }
        
        .test-value {
            font-weight: bold;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }
        
        .good {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .warning {
            background: #fef5e7;
            color: #744210;
        }
        
        .error {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .test-button {
            background: #38a169;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
            min-height: 44px;
            min-width: 120px;
        }
        
        .test-button:hover {
            background: #2f855a;
        }
        
        .test-button:active {
            transform: scale(0.98);
        }
        
        .navigation-test {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .navigation-test a {
            background: #1a365d;
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .navigation-test a:hover {
            background: #2d5aa0;
            transform: translateY(-2px);
        }
        
        .device-info {
            background: #e6fffa;
            border: 1px solid #38b2ac;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .device-info h3 {
            color: #234e52;
            margin-bottom: 0.5rem;
        }
        
        .device-info p {
            color: #285e61;
            margin-bottom: 0.3rem;
        }
        
        @media (max-width: 768px) {
            .test-container {
                padding: 1rem;
                margin: 10px;
            }
            
            h1 {
                font-size: 1.8rem;
            }
            
            .test-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .navigation-test {
                flex-direction: column;
            }
            
            .navigation-test a {
                width: 100%;
                text-align: center;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .test-container {
                padding: 0.8rem;
            }
            
            h1 {
                font-size: 1.6rem;
            }
            
            .test-section {
                padding: 1rem;
            }
            
            .test-section h2 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 اختبار الاستجابة للهواتف</h1>
        
        <div class="test-section">
            <h2>📱 معلومات الجهاز</h2>
            <div class="test-item">
                <span class="test-label">عرض الشاشة:</span>
                <span class="test-value" id="screenWidth">جاري التحميل...</span>
            </div>
            <div class="test-item">
                <span class="test-label">ارتفاع الشاشة:</span>
                <span class="test-value" id="screenHeight">جاري التحميل...</span>
            </div>
            <div class="test-item">
                <span class="test-label">نوع الجهاز:</span>
                <span class="test-value" id="deviceType">جاري التحميل...</span>
            </div>
            <div class="test-item">
                <span class="test-label">المتصفح:</span>
                <span class="test-value" id="browser">جاري التحميل...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🎯 اختبار مناطق اللمس</h2>
            <p style="margin-bottom: 1rem; color: #4a5568;">جرب الضغط على الأزرار التالية للتأكد من سهولة اللمس:</p>
            <div style="display: flex; flex-wrap: wrap; gap: 1rem;">
                <button class="test-button" onclick="testTouch(this)">زر كبير ✓</button>
                <button class="test-button" onclick="testTouch(this)">زر متوسط</button>
                <button class="test-button" onclick="testTouch(this)">اختبار اللمس</button>
            </div>
            <div id="touchResult" style="margin-top: 1rem; padding: 1rem; background: #f0f8ff; border-radius: 8px; display: none;">
                <strong>نتيجة الاختبار:</strong> <span id="touchMessage"></span>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🧭 اختبار التنقل</h2>
            <p style="margin-bottom: 1rem; color: #4a5568;">جرب الروابط التالية للتأكد من سهولة التنقل:</p>
            <div class="navigation-test">
                <a href="index.html">الصفحة الرئيسية</a>
                <a href="about.html">من هو الأستاذ فراس</a>
                <a href="program.html">البرنامج الانتخابي</a>
                <a href="contact.html">اتصل بنا</a>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 تقييم الاستجابة</h2>
            <div class="test-item">
                <span class="test-label">حجم الخط:</span>
                <span class="test-value" id="fontSizeTest">جاري الفحص...</span>
            </div>
            <div class="test-item">
                <span class="test-label">مناطق اللمس:</span>
                <span class="test-value" id="touchAreaTest">جاري الفحص...</span>
            </div>
            <div class="test-item">
                <span class="test-label">التخطيط:</span>
                <span class="test-value" id="layoutTest">جاري الفحص...</span>
            </div>
            <div class="test-item">
                <span class="test-label">التقييم العام:</span>
                <span class="test-value" id="overallTest">جاري الفحص...</span>
            </div>
        </div>
        
        <div class="device-info">
            <h3>💡 نصائح للاختبار:</h3>
            <p>• جرب تدوير الجهاز للوضع الأفقي والعمودي</p>
            <p>• اختبر التمرير والتنقل بين الصفحات</p>
            <p>• تأكد من وضوح النصوص وسهولة القراءة</p>
            <p>• جرب فتح وإغلاق قائمة التنقل</p>
            <p>• اختبر النماذج والأزرار</p>
        </div>
    </div>

    <script>
        // تحديث معلومات الجهاز
        function updateDeviceInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            
            document.getElementById('screenWidth').textContent = width + 'px';
            document.getElementById('screenHeight').textContent = height + 'px';
            
            // تحديد نوع الجهاز
            let deviceType = 'سطح المكتب';
            let deviceClass = 'good';
            
            if (width <= 480) {
                deviceType = 'هاتف صغير';
                deviceClass = 'warning';
            } else if (width <= 768) {
                deviceType = 'هاتف/تابلت';
                deviceClass = 'good';
            } else if (width <= 1024) {
                deviceType = 'تابلت كبير';
                deviceClass = 'good';
            }
            
            const deviceElement = document.getElementById('deviceType');
            deviceElement.textContent = deviceType;
            deviceElement.className = `test-value ${deviceClass}`;
            
            // معلومات المتصفح
            const browser = navigator.userAgent.includes('Chrome') ? 'Chrome' :
                           navigator.userAgent.includes('Firefox') ? 'Firefox' :
                           navigator.userAgent.includes('Safari') ? 'Safari' :
                           navigator.userAgent.includes('Edge') ? 'Edge' : 'غير معروف';
            
            document.getElementById('browser').textContent = browser;
            
            // تقييم الاستجابة
            evaluateResponsiveness(width);
        }
        
        // تقييم الاستجابة
        function evaluateResponsiveness(width) {
            // فحص حجم الخط
            const fontSize = parseFloat(getComputedStyle(document.body).fontSize);
            const fontSizeElement = document.getElementById('fontSizeTest');
            
            if (fontSize >= 16) {
                fontSizeElement.textContent = 'ممتاز';
                fontSizeElement.className = 'test-value good';
            } else if (fontSize >= 14) {
                fontSizeElement.textContent = 'جيد';
                fontSizeElement.className = 'test-value warning';
            } else {
                fontSizeElement.textContent = 'يحتاج تحسين';
                fontSizeElement.className = 'test-value error';
            }
            
            // فحص مناطق اللمس
            const buttons = document.querySelectorAll('.test-button');
            const buttonHeight = buttons[0] ? buttons[0].offsetHeight : 0;
            const touchAreaElement = document.getElementById('touchAreaTest');
            
            if (buttonHeight >= 44) {
                touchAreaElement.textContent = 'ممتاز';
                touchAreaElement.className = 'test-value good';
            } else if (buttonHeight >= 36) {
                touchAreaElement.textContent = 'جيد';
                touchAreaElement.className = 'test-value warning';
            } else {
                touchAreaElement.textContent = 'يحتاج تحسين';
                touchAreaElement.className = 'test-value error';
            }
            
            // فحص التخطيط
            const layoutElement = document.getElementById('layoutTest');
            if (width <= 768) {
                layoutElement.textContent = 'محسن للهواتف';
                layoutElement.className = 'test-value good';
            } else {
                layoutElement.textContent = 'تخطيط سطح المكتب';
                layoutElement.className = 'test-value good';
            }
            
            // التقييم العام
            const overallElement = document.getElementById('overallTest');
            const goodCount = document.querySelectorAll('.good').length;
            const totalTests = 3;
            
            if (goodCount === totalTests) {
                overallElement.textContent = 'ممتاز ✓';
                overallElement.className = 'test-value good';
            } else if (goodCount >= totalTests - 1) {
                overallElement.textContent = 'جيد جداً';
                overallElement.className = 'test-value warning';
            } else {
                overallElement.textContent = 'يحتاج تحسين';
                overallElement.className = 'test-value error';
            }
        }
        
        // اختبار اللمس
        function testTouch(button) {
            const result = document.getElementById('touchResult');
            const message = document.getElementById('touchMessage');
            
            result.style.display = 'block';
            message.textContent = 'تم الضغط بنجاح! الزر يعمل بشكل صحيح.';
            
            // تأثير بصري
            button.style.background = '#22543d';
            setTimeout(() => {
                button.style.background = '#38a169';
            }, 200);
        }
        
        // تحديث المعلومات عند تحميل الصفحة وتغيير حجم النافذة
        window.addEventListener('load', updateDeviceInfo);
        window.addEventListener('resize', updateDeviceInfo);
        
        // اختبار التوجه
        window.addEventListener('orientationchange', function() {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>