<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اتصل بنا - الأستاذ فراس رحيم مجيسر</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .contact-hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: var(--light-color);
            padding: 140px 20px 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M0 0h100v100H0z' fill='none'/%3E%3Ccircle cx='25' cy='25' r='10' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3Ccircle cx='75' cy='75' r='10' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3C/svg%3E") repeat;
            opacity: 0.1;
        }

        .contact-content {
            padding: 80px 20px;
            background: var(--light-color);
        }

        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .contact-info {
            background: var(--background-light);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
        }

        .contact-info h2 {
            color: var(--primary-color);
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
        }

        .contact-info p {
            color: var(--text-light);
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .contact-methods {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .contact-method {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: var(--light-color);
            border-radius: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .contact-method:hover {
            transform: translateX(-10px);
            box-shadow: var(--shadow);
        }

        .contact-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--secondary-color), #2f855a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .contact-method:hover .contact-icon {
            transform: rotateY(360deg);
        }

        .contact-icon i {
            font-size: 1.5rem;
            color: var(--light-color);
        }

        .contact-details h3 {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin-bottom: 0.3rem;
        }

        .contact-details p {
            color: var(--text-dark);
            margin: 0;
            font-weight: 600;
        }

        .contact-form {
            background: var(--background-light);
            padding: 3rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
        }

        .contact-form h2 {
            color: var(--primary-color);
            font-size: 2.2rem;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: var(--light-color);
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
            transform: translateY(-2px);
            outline: none;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .social-contact {
            padding: 80px 20px;
            background: var(--background-light);
        }

        .social-contact h2 {
            text-align: center;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }

        .social-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .social-card {
            background: var(--light-color);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .social-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(56, 161, 105, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .social-card:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .social-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .social-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        .social-card:hover .social-icon {
            transform: rotateY(360deg) scale(1.1);
        }

        .facebook .social-icon {
            background: linear-gradient(135deg, #1877f2, #42a5f5);
        }

        .telegram .social-icon {
            background: linear-gradient(135deg, #0088cc, #26a69a);
        }

        .instagram .social-icon {
            background: linear-gradient(135deg, #e4405f, #fd1d1d, #fcb045);
        }

        .youtube .social-icon {
            background: linear-gradient(135deg, #ff0000, #ff5722);
        }

        .tiktok .social-icon {
            background: linear-gradient(135deg, #000000, #ff0050);
        }

        .social-icon i {
            font-size: 2rem;
            color: var(--light-color);
        }

        .social-card h3 {
            color: var(--primary-color);
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .social-card p {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .social-card a {
            display: inline-block;
            padding: 10px 25px;
            background: var(--secondary-color);
            color: var(--light-color);
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .social-card a:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }

        .office-hours {
            padding: 80px 20px;
            background: var(--primary-color);
            color: var(--light-color);
        }

        .office-hours h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }

        .hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .hours-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .hours-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .hours-card i {
            font-size: 3rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
            display: block;
            text-align: center;
        }

        .hours-card h3 {
            text-align: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .hours-list {
            list-style: none;
            padding: 0;
        }

        .hours-list li {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hours-list li:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .social-grid {
                grid-template-columns: 1fr;
            }

            .hours-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h2>الأستاذ فراس رحيم مجيسر</h2>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html">الرئيسية</a></li>
                    <li><a href="about.html">من هو الأستاذ فراس</a></li>
                    <li><a href="program.html">البرنامج الانتخابي</a></li>
                    <li><a href="achievements.html">الإنجازات</a></li>
                    <li><a href="gallery.html">معرض الصور</a></li>
                    <li><a href="join.html">انضم للحملة</a></li>
                    <li><a href="contact.html" class="active">اتصل بنا</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- قسم البطل -->
    <section class="contact-hero">
        <div class="container">
            <h1>تواصل معنا</h1>
            <p>نحن هنا للاستماع إليك ومساعدتك في كل ما تحتاجه</p>
        </div>
    </section>

    <!-- محتوى الاتصال -->
    <section class="contact-content">
        <div class="container">
            <div class="contact-grid">
                <!-- معلومات الاتصال -->
                <div class="contact-info">
                    <h2>كيف تتواصل معنا؟</h2>
                    <p>نحن متاحون دائماً للاستماع لآرائكم واقتراحاتكم. تواصلوا معنا عبر أي من الطرق التالية وسنرد عليكم في أقرب وقت ممكن.</p>
                    
                    <div class="contact-methods">
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h3>الهاتف</h3>
                                <p>+964 ************</p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h3>البريد الإلكتروني</h3>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h3>العنوان</h3>
                                <p>بغداد، العراق</p>
                            </div>
                        </div>
                        
                        <div class="contact-method">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-details">
                                <h3>ساعات العمل</h3>
                                <p>الأحد - الخميس: 9:00 - 17:00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- نموذج الاتصال -->
                <div class="contact-form">
                    <h2>أرسل لنا رسالة</h2>
                    <form id="contactForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">الاسم الأول</label>
                                <input type="text" id="firstName" name="firstName" required>
                            </div>
                            <div class="form-group">
                                <label for="lastName">الاسم الأخير</label>
                                <input type="text" id="lastName" name="lastName" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">الموضوع</label>
                            <select id="subject" name="subject" required>
                                <option value="">اختر الموضوع</option>
                                <option value="general">استفسار عام</option>
                                <option value="support">طلب دعم</option>
                                <option value="suggestion">اقتراح</option>
                                <option value="complaint">شكوى</option>
                                <option value="volunteer">التطوع</option>
                                <option value="media">استفسار إعلامي</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">الرسالة</label>
                            <textarea id="message" name="message" rows="5" placeholder="اكتب رسالتك هنا..." required></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary" style="width: 100%;">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- وسائل التواصل الاجتماعي -->
    <section class="social-contact">
        <div class="container">
            <h2>تابعنا على وسائل التواصل الاجتماعي</h2>
            <div class="social-grid">
                <div class="social-card facebook">
                    <div class="social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </div>
                    <h3>فيسبوك</h3>
                    <p>تابع آخر الأخبار والفعاليات على صفحتنا الرسمية في فيسبوك</p>
                    <a href="https://www.facebook.com/share/1N5yyHKSvV/" target="_blank" rel="noopener noreferrer">زيارة الصفحة</a>
                </div>
                
                <div class="social-card telegram">
                    <div class="social-icon">
                        <i class="fab fa-telegram-plane"></i>
                    </div>
                    <h3>تيليجرام</h3>
                    <p>انضم لقناتنا على تيليجرام للحصول على التحديثات الفورية</p>
                    <a href="#" target="_blank">انضم للقناة</a>
                </div>
                
                <div class="social-card instagram">
                    <div class="social-icon">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <h3>إنستجرام</h3>
                    <p>شاهد الصور والفيديوهات من فعالياتنا ومبادراتنا المختلفة</p>
                    <a href="#" target="_blank">تابعنا</a>
                </div>
                
                <div class="social-card tiktok">
                    <div class="social-icon">
                        <i class="fab fa-tiktok"></i>
                    </div>
                    <h3>تيك توك</h3>
                    <p>تابع أحدث الفيديوهات والمحتوى التفاعلي من حملتنا الانتخابية</p>
                    <a href="https://www.tiktok.com/@firasmjiser" target="_blank" rel="noopener noreferrer">تابعنا</a>
                </div>
                
                <div class="social-card youtube">
                    <div class="social-icon">
                        <i class="fab fa-youtube"></i>
                    </div>
                    <h3>يوتيوب</h3>
                    <p>شاهد الفيديوهات والمقابلات والتغطيات المصورة لأنشطتنا</p>
                    <a href="#" target="_blank">زيارة القناة</a>
                </div>
            </div>
        </div>
    </section>

    <!-- ساعات العمل -->
    <section class="office-hours">
        <div class="container">
            <h2>ساعات العمل والاستقبال</h2>
            <div class="hours-grid">
                <div class="hours-card">
                    <i class="fas fa-building"></i>
                    <h3>المكتب الرئيسي</h3>
                    <ul class="hours-list">
                        <li><span>الأحد - الخميس</span><span>9:00 - 17:00</span></li>
                        <li><span>الجمعة</span><span>مغلق</span></li>
                        <li><span>السبت</span><span>10:00 - 14:00</span></li>
                    </ul>
                </div>
                
                <div class="hours-card">
                    <i class="fas fa-phone-alt"></i>
                    <h3>الخط الساخن</h3>
                    <ul class="hours-list">
                        <li><span>الأحد - الخميس</span><span>8:00 - 20:00</span></li>
                        <li><span>الجمعة</span><span>10:00 - 16:00</span></li>
                        <li><span>السبت</span><span>9:00 - 18:00</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>الأستاذ فراس رحيم مجيسر</h3>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                    <div class="social-links">
                        <a href="https://www.facebook.com/share/1N5yyHKSvV/" target="_blank" rel="noopener noreferrer"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-telegram"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="https://www.tiktok.com/@firasmjiser" target="_blank" rel="noopener noreferrer"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="about.html">من هو الأستاذ فراس</a></li>
                        <li><a href="program.html">البرنامج الانتخابي</a></li>
                        <li><a href="achievements.html">الإنجازات</a></li>
                        <li><a href="join.html">انضم للحملة</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +964 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> بغداد، العراق</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 الأستاذ فراس رحيم مجيسر. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>