<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإنجازات - الأستاذ فراس رحيم مجيسر</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .achievements-hero {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #2f855a 100%);
            color: var(--light-color);
            padding: 140px 20px 80px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .achievements-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M0 0h100v100H0z' fill='none'/%3E%3Cpolygon points='50,20 60,40 80,40 65,55 70,75 50,65 30,75 35,55 20,40 40,40' fill='rgba(255,255,255,0.1)'/%3E%3C/svg%3E") repeat;
            opacity: 0.1;
        }

        .achievements-overview {
            padding: 80px 20px;
            background: var(--light-color);
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .overview-card {
            background: linear-gradient(135deg, var(--background-light), #f0f8ff);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .overview-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(56, 161, 105, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .overview-card:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .overview-card:hover {
            transform: translateY(-15px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .overview-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--secondary-color), #2f855a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            transition: all 0.3s ease;
        }

        .overview-card:hover .overview-icon {
            transform: rotateY(360deg) scale(1.1);
        }

        .overview-icon i {
            font-size: 2.5rem;
            color: var(--light-color);
        }

        .overview-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .overview-label {
            font-size: 1.2rem;
            color: var(--text-dark);
            font-weight: 600;
        }

        .achievements-categories {
            padding: 80px 20px;
            background: var(--background-light);
        }

        .categories-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 3rem;
        }

        .category-section {
            margin-bottom: 4rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .category-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: var(--light-color);
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .category-header i {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .category-header h2 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .category-content {
            background: var(--light-color);
            padding: 3rem;
            border-radius: 0 0 20px 20px;
            box-shadow: var(--shadow);
        }

        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .achievement-card {
            background: var(--background-light);
            padding: 2rem;
            border-radius: 15px;
            border-right: 4px solid var(--secondary-color);
            transition: all 0.3s ease;
            position: relative;
        }

        .achievement-card:hover {
            transform: translateX(-10px);
            box-shadow: var(--shadow);
            border-right-color: var(--primary-color);
        }

        .achievement-card h3 {
            color: var(--primary-color);
            font-size: 1.3rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .achievement-card h3 i {
            color: var(--secondary-color);
        }

        .achievement-card p {
            color: var(--text-light);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .achievement-stats {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            background: var(--light-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            color: var(--secondary-color);
            font-weight: 600;
        }

        .testimonials {
            padding: 80px 20px;
            background: var(--primary-color);
            color: var(--light-color);
        }

        .testimonials h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .testimonial-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            transform: translateY(-10px);
            background: rgba(255, 255, 255, 0.15);
        }

        .testimonial-quote {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            font-style: italic;
            position: relative;
        }

        .testimonial-quote::before {
            content: '"';
            font-size: 4rem;
            color: var(--secondary-color);
            position: absolute;
            top: -20px;
            right: -10px;
            opacity: 0.5;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .author-avatar {
            width: 60px;
            height: 60px;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .author-info h4 {
            margin-bottom: 0.2rem;
            color: var(--secondary-color);
        }

        .author-info p {
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .awards-section {
            padding: 80px 20px;
            background: var(--light-color);
        }

        .awards-title {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-color);
            margin-bottom: 3rem;
        }

        .awards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 0 auto;
        }

        .award-card {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .award-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .award-card:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .award-card:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 20px 40px rgba(255, 215, 0, 0.4);
        }

        .award-icon {
            font-size: 4rem;
            color: #b8860b;
            margin-bottom: 1rem;
        }

        .award-card h3 {
            color: #8b4513;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .award-card p {
            color: #654321;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .achievements-grid {
                grid-template-columns: 1fr;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .awards-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h2>الأستاذ فراس رحيم مجيسر</h2>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.html">الرئيسية</a></li>
                    <li><a href="about.html">من هو الأستاذ فراس</a></li>
                    <li><a href="program.html">البرنامج الانتخابي</a></li>
                    <li><a href="achievements.html" class="active">الإنجازات</a></li>
                    <li><a href="gallery.html">معرض الصور</a></li>
                    <li><a href="join.html">انضم للحملة</a></li>
                    <li><a href="contact.html">اتصل بنا</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- قسم البطل -->
    <section class="achievements-hero">
        <div class="container">
            <h1>الإنجازات والمبادرات</h1>
            <p>15 عاماً من العطاء المستمر والعمل الدؤوب لخدمة المجتمع</p>
        </div>
    </section>

    <!-- نظرة عامة على الإنجازات -->
    <section class="achievements-overview">
        <div class="container">
            <div class="overview-grid">
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="overview-number">500+</div>
                    <div class="overview-label">عائلة مدعومة</div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="overview-number">25+</div>
                    <div class="overview-label">مشروع تنموي</div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="overview-number">10+</div>
                    <div class="overview-label">شركة ومؤسسة</div>
                </div>
                <div class="overview-card">
                    <div class="overview-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="overview-number">15</div>
                    <div class="overview-label">سنة من العطاء</div>
                </div>
            </div>
        </div>
    </section>

    <!-- فئات الإنجازات -->
    <section class="achievements-categories">
        <div class="container">
            <h2 class="categories-title">إنجازات متنوعة في خدمة المجتمع</h2>

            <!-- الأعمال الخيرية -->
            <div class="category-section">
                <div class="category-header">
                    <i class="fas fa-heart"></i>
                    <h2>الأعمال الخيرية والإنسانية</h2>
                    <p>مبادرات لدعم الفقراء والمحتاجين</p>
                </div>
                <div class="category-content">
                    <div class="achievements-grid">
                        <div class="achievement-card">
                            <h3><i class="fas fa-child"></i> برنامج دعم الأيتام</h3>
                            <p>إطلاق برنامج شامل لدعم 200 طفل يتيم في بغداد، يشمل الدعم المالي والتعليمي والصحي.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">200 طفل</span>
                                <span class="stat-item">مستمر</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-home"></i> مشروع الإسكان الاجتماعي</h3>
                            <p>توفير سكن لائق لـ 50 عائلة من ذوي الدخل المحدود في مناطق مختلفة من بغداد.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">50 عائلة</span>
                                <span class="stat-item">مكتمل</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-shopping-basket"></i> السلة الغذائية الشهرية</h3>
                            <p>توزيع السلة الغذائية على 300 عائلة محتاجة شهرياً لضمان الأمن الغذائي.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">300 عائلة</span>
                                <span class="stat-item">شهرياً</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التنمية الاقتصادية -->
            <div class="category-section">
                <div class="category-header">
                    <i class="fas fa-chart-line"></i>
                    <h2>التنمية الاقتصادية</h2>
                    <p>مشاريع لتحفيز الاقتصاد المحلي</p>
                </div>
                <div class="category-content">
                    <div class="achievements-grid">
                        <div class="achievement-card">
                            <h3><i class="fas fa-store"></i> دعم المشاريع الصغيرة</h3>
                            <p>تمويل وتدريب 100 شاب وشابة لإطلاق مشاريعهم الصغيرة في مجالات مختلفة.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">100 مشروع</span>
                                <span class="stat-item">نشط</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-briefcase"></i> توفير فرص العمل</h3>
                            <p>خلق 500 فرصة عمل مباشرة وغير مباشرة من خلال الشركات والمؤسسات المختلفة.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">500 وظيفة</span>
                                <span class="stat-item">مستمر</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-graduation-cap"></i> برامج التدريب المهني</h3>
                            <p>تدريب 200 شخص على مهارات مختلفة لتحسين قدرتهم على الحصول على عمل.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">200 متدرب</span>
                                <span class="stat-item">مكتمل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- التعليم والثقافة -->
            <div class="category-section">
                <div class="category-header">
                    <i class="fas fa-book"></i>
                    <h2>التعليم والثقافة</h2>
                    <p>استثمار في التعليم والتطوير الثقافي</p>
                </div>
                <div class="category-content">
                    <div class="achievements-grid">
                        <div class="achievement-card">
                            <h3><i class="fas fa-school"></i> دعم المدارس</h3>
                            <p>تجهيز وصيانة 15 مدرسة في المناطق الشعبية وتوفير المستلزمات التعليمية.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">15 مدرسة</span>
                                <span class="stat-item">مكتمل</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-laptop"></i> مراكز الحاسوب</h3>
                            <p>إنشاء 5 مراكز حاسوب مجانية لتعليم الشباب المهارات التقنية الحديثة.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">5 مراكز</span>
                                <span class="stat-item">نشط</span>
                            </div>
                        </div>
                        <div class="achievement-card">
                            <h3><i class="fas fa-book-reader"></i> مكتبات مجتمعية</h3>
                            <p>تأسيس 8 مكتبات مجتمعية لتشجيع القراءة والثقافة في الأحياء المختلفة.</p>
                            <div class="achievement-stats">
                                <span class="stat-item">8 مكتبات</span>
                                <span class="stat-item">نشط</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- شهادات وتقديرات -->
    <section class="testimonials">
        <div class="container">
            <h2>شهادات من المجتمع</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-quote">
                        الأستاذ فراس شخص استثنائي، ساعدني في إطلاق مشروعي الصغير وأصبحت الآن أعيل عائلتي بكرامة. جزاه الله خيراً.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">أ.م</div>
                        <div class="author-info">
                            <h4>أحمد محمد</h4>
                            <p>صاحب مشروع صغير</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-quote">
                        بفضل برنامج دعم الأيتام، أطفالي يحصلون على تعليم جيد ورعاية صحية. الأستاذ فراس ملاك رحمة في حياتنا.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">ف.ع</div>
                        <div class="author-info">
                            <h4>فاطمة علي</h4>
                            <p>أم لثلاثة أيتام</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-quote">
                        المركز التدريبي الذي أسسه الأستاذ فراس غير حياتي. تعلمت مهارات جديدة وحصلت على عمل محترم.
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">س.ح</div>
                        <div class="author-info">
                            <h4>سارة حسن</h4>
                            <p>خريجة برنامج التدريب</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- الجوائز والتكريمات -->
    <section class="awards-section">
        <div class="container">
            <h2 class="awards-title">الجوائز والتكريمات</h2>
            <div class="awards-grid">
                <div class="award-card">
                    <div class="award-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3>جائزة أفضل رجل أعمال</h3>
                    <p>تكريم من غرفة تجارة بغداد لإسهاماته في التنمية الاقتصادية المحلية</p>
                </div>
                <div class="award-card">
                    <div class="award-icon">
                        <i class="fas fa-medal"></i>
                    </div>
                    <h3>وسام العطاء الإنساني</h3>
                    <p>تقدير من منظمات المجتمع المدني لجهوده في العمل الخيري والإنساني</p>
                </div>
                <div class="award-card">
                    <div class="award-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3>شهادة التميز المجتمعي</h3>
                    <p>اعتراف بدوره الفعال في خدمة المجتمع وتطوير المناطق الشعبية</p>
                </div>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>الأستاذ فراس رحيم مجيسر</h3>
                    <p>صوتٌ للعطاء، وعدٌ للتغيير</p>
                    <div class="social-links">
                        <a href="https://www.facebook.com/share/1N5yyHKSvV/" target="_blank" rel="noopener noreferrer"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-telegram"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="https://www.tiktok.com/@firasmjiser" target="_blank" rel="noopener noreferrer"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="about.html">من هو الأستاذ فراس</a></li>
                        <li><a href="program.html">البرنامج الانتخابي</a></li>
                        <li><a href="achievements.html">الإنجازات</a></li>
                        <li><a href="join.html">انضم للحملة</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-phone"></i> +964 ************</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-map-marker-alt"></i> بغداد، العراق</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 الأستاذ فراس رحيم مجيسر. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>