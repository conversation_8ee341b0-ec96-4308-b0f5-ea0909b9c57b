/* تحسينات الأداء للهواتف */

/* تحسين الخطوط للهواتف */
@media (max-width: 768px) {
    /* تحسين تحميل الخطوط */
    @font-face {
        font-family: 'Cairo';
        font-display: swap;
        src: local('Cairo');
    }
    
    /* تقليل استخدام الخطوط الثقيلة */
    * {
        font-weight: 400 !important;
    }
    
    h1, h2, h3, .btn {
        font-weight: 600 !important;
    }
}

/* تحسين الصور للهواتف */
@media (max-width: 768px) {
    img {
        /* تحسين جودة الصور */
        image-rendering: optimizeQuality;
        /* تحسين التحميل */
        loading: lazy;
        /* منع التمدد */
        object-fit: cover;
        /* تحسين الحدود */
        border-radius: 10px;
    }
    
    /* تحسين صور SVG */
    svg {
        max-width: 100%;
        height: auto;
    }
}

/* تحسين التأثيرات للهواتف */
@media (max-width: 768px) {
    /* تقليل التأثيرات المعقدة */
    * {
        will-change: auto !important;
    }
    
    /* تحسين الانتقالات */
    .btn, .nav-menu a, .vision-card, .news-card {
        transition: transform 0.2s ease, background-color 0.2s ease !important;
    }
    
    /* إزالة التأثيرات الثقيلة */
    .floating-particle,
    .hero::after {
        display: none !important;
    }
    
    /* تحسين الظلال */
    .vision-card, .news-card, .btn {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }
}

/* تحسين التمرير للهواتف */
@media (max-width: 768px) {
    html {
        /* تمرير سلس محسن */
        scroll-behavior: smooth;
        /* تحسين التمرير على iOS */
        -webkit-overflow-scrolling: touch;
    }
    
    body {
        /* منع الارتداد */
        overscroll-behavior: none;
        /* تحسين الأداء */
        transform: translateZ(0);
    }
    
    /* تحسين العناصر المتحركة */
    .hero, .stats, .vision {
        /* تحسين الطبقات */
        transform: translateZ(0);
        /* تحسين الرسم */
        backface-visibility: hidden;
    }
}

/* تحسين النماذج للهواتف */
@media (max-width: 768px) {
    input, textarea, select {
        /* تحسين الحجم */
        font-size: 16px !important;
        /* منع التكبير التلقائي */
        transform: translateZ(0);
        /* تحسين الحدود */
        border-radius: 8px;
        /* تحسين الحشو */
        padding: 12px 15px;
    }
    
    /* تحسين الأزرار */
    .btn {
        /* حجم مناسب للمس */
        min-height: 48px;
        /* حشو مناسب */
        padding: 14px 20px;
        /* خط واضح */
        font-size: 16px;
        /* تحسين اللمس */
        touch-action: manipulation;
    }
}

/* تحسين الجداول للهواتف */
@media (max-width: 768px) {
    table {
        /* تمرير أفقي */
        overflow-x: auto;
        /* عرض كامل */
        width: 100%;
        /* تحسين العرض */
        display: block;
        white-space: nowrap;
    }
    
    th, td {
        /* حشو مناسب */
        padding: 8px 12px;
        /* حجم خط مناسب */
        font-size: 14px;
    }
}

/* تحسين القوائم للهواتف */
@media (max-width: 768px) {
    ul, ol {
        /* حشو مناسب */
        padding-right: 20px;
    }
    
    li {
        /* تباعد مناسب */
        margin-bottom: 8px;
        /* ارتفاع سطر مناسب */
        line-height: 1.6;
    }
}

/* تحسين الوسائط للهواتف */
@media (max-width: 768px) {
    video, iframe {
        /* عرض كامل */
        width: 100% !important;
        /* ارتفاع تلقائي */
        height: auto !important;
        /* حد أقصى للعرض */
        max-width: 100%;
    }
    
    /* تحسين الخرائط */
    .map-container {
        /* ارتفاع مناسب */
        height: 300px;
        /* عرض كامل */
        width: 100%;
    }
}

/* تحسين الطباعة للهواتف */
@media print {
    /* إخفاء العناصر غير الضرورية */
    .header, .footer, .btn, .hamburger {
        display: none !important;
    }
    
    /* تحسين النصوص */
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }
    
    /* تحسين الصفحات */
    * {
        box-shadow: none !important;
        text-shadow: none !important;
    }
}

/* تحسين الوضع المظلم (للمستقبل) */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    :root {
        --primary-color: #4299e1;
        --secondary-color: #68d391;
        --text-dark: #e2e8f0;
        --text-light: #a0aec0;
        --background-light: #2d3748;
        --light-color: #1a202c;
    }
}

/* تحسين الحركة المقللة */
@media (prefers-reduced-motion: reduce) and (max-width: 768px) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .floating-particle,
    .hero::after,
    .parallax {
        display: none !important;
    }
}

/* تحسين التباين العالي */
@media (prefers-contrast: high) and (max-width: 768px) {
    .btn {
        border: 2px solid currentColor;
    }
    
    .vision-card, .news-card {
        border: 1px solid var(--text-dark);
    }
    
    a {
        text-decoration: underline;
    }
}

/* تحسين الشفافية المقللة */
@media (prefers-reduced-transparency: reduce) and (max-width: 768px) {
    .nav-menu,
    .lightbox,
    .success-message {
        backdrop-filter: none !important;
        background: solid !important;
    }
}

/* تحسين البيانات المحدودة */
@media (max-width: 768px) and (prefers-reduced-data: reduce) {
    /* تقليل الصور */
    img[src$=".jpg"], img[src$=".png"] {
        display: none;
    }
    
    /* الاعتماد على SVG فقط */
    img[src$=".svg"] {
        display: block;
    }
    
    /* تقليل الخطوط */
    @font-face {
        font-display: optional;
    }
    
    /* إزالة التأثيرات */
    * {
        animation: none !important;
        transition: none !important;
        transform: none !important;
    }
}

/* تحسين الأجهزة الضعيفة */
@media (max-width: 768px) {
    /* للأجهزة ذات الذاكرة المحدودة */
    @supports (content-visibility: auto) {
        .vision-card, .news-card {
            content-visibility: auto;
            contain-intrinsic-size: 300px;
        }
    }
    
    /* تحسين الرسم */
    .hero, .stats, .vision, .news {
        contain: layout style paint;
    }
    
    /* تحسين التحديث */
    .btn:hover, .vision-card:hover {
        contain: layout;
    }
}

/* تحسين الشبكة البطيئة */
@media (max-width: 768px) and (prefers-reduced-data: reduce) {
    /* تأخير تحميل الصور */
    img {
        loading: lazy;
    }
    
    /* تقليل جودة الصور */
    img {
        image-rendering: optimizeSpeed;
    }
    
    /* إزالة الخطوط الإضافية */
    @import url() {
        display: none;
    }
}

/* تحسين البطارية المنخفضة */
@media (max-width: 768px) {
    /* تقليل معدل الإطارات */
    @keyframes reducedMotion {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    .fade-in {
        animation: reducedMotion 0.3s ease-out;
    }
    
    /* تقليل استخدام المعالج */
    * {
        will-change: auto;
    }
    
    /* تحسين الذاكرة */
    .floating-particle {
        display: none;
    }
}