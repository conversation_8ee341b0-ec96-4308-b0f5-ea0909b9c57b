/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1a365d;      /* أزرق داكن */
    --secondary-color: #38a169;    /* أخضر زيتوني */
    --accent-color: #2d5aa0;       /* أزرق متوسط */
    --light-color: #ffffff;        /* أبيض */
    --text-dark: #2d3748;          /* نص داكن */
    --text-light: #4a5568;         /* نص فاتح */
    --border-color: #e2e8f0;       /* حدود */
    --background-light: #f7fafc;   /* خلفية فاتحة */
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* أزرار */
.btn {
    display: inline-block;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 16px;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--light-color);
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--secondary-color);
    color: var(--light-color);
}

.btn-secondary:hover {
    background: #2f855a;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--light-color);
    transform: translateY(-2px);
}

/* الهيدر */
.header {
    background: var(--light-color);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.logo h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0.2rem;
}

.logo p {
    color: var(--secondary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-menu a:hover {
    color: var(--primary-color);
    background: var(--background-light);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: var(--transition);
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* القسم الرئيسي */
.hero {
    background: linear-gradient(135deg, var(--background-light) 0%, #e8f5e8 100%);
    padding: 140px 20px 80px;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-text h1 {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-text h2 {
    font-size: 1.8rem;
    color: var(--secondary-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.hero-text p {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    text-align: center;
}

.candidate-photo {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 20px;
    box-shadow: var(--shadow);
    border: 5px solid var(--light-color);
}

/* قسم الرؤية */
.vision {
    padding: 80px 20px;
    background: var(--light-color);
}

.vision h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.vision-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.vision-card {
    background: var(--background-light);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
}

.vision-card:hover {
    transform: translateY(-10px);
    border-color: var(--secondary-color);
    box-shadow: var(--shadow);
}

.vision-card i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.vision-card h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.vision-card p {
    color: var(--text-light);
    line-height: 1.6;
}

/* قسم الإحصائيات */
.stats {
    padding: 60px 20px;
    background: var(--primary-color);
    color: var(--light-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    text-align: center;
}

.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
}

/* قسم الأخبار */
.news {
    padding: 80px 20px;
    background: var(--background-light);
}

.news h2 {
    text-align: center;
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 3rem;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.news-card {
    background: var(--light-color);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.news-content {
    padding: 1.5rem;
}

.news-content h3 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
}

.news-content p {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.news-date {
    color: var(--secondary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

/* الفوتر */
.footer {
    background: var(--text-dark);
    color: var(--light-color);
    padding: 50px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-section p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    color: var(--light-color);
    font-size: 1.5rem;
    transition: var(--transition);
}

.social-links a:hover {
    color: var(--secondary-color);
    transform: translateY(-3px);
}

/* تنسيق خاص لأيقونة TikTok */
.social-links a .fa-tiktok {
    color: var(--light-color);
}

.social-links a:hover .fa-tiktok {
    color: #ff0050;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--light-color);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--secondary-color);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.contact-info i {
    color: var(--secondary-color);
    width: 20px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #4a5568;
}

/* استجابة للشاشات الصغيرة */

/* تحسينات عامة للهواتف */
@media (max-width: 768px) {
    /* تحسين الحاوي العام */
    .container {
        padding: 0 15px;
    }
    
    /* تحسين الهيدر */
    .hamburger {
        display: flex;
        z-index: 1001;
    }
    
    .nav-container {
        padding: 0 15px;
    }
    
    .logo h2 {
        font-size: 1.2rem;
    }
    
    .logo p {
        font-size: 0.8rem;
    }
    
    .nav-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 2rem;
        gap: 2rem;
        z-index: 1000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }
    
    .nav-menu.active {
        display: flex;
        transform: translateX(0);
    }
    
    .nav-menu a {
        font-size: 1.2rem;
        padding: 1rem 2rem;
        border-radius: 15px;
        width: 100%;
        text-align: center;
        background: var(--background-light);
        margin-bottom: 0.5rem;
    }
    
    /* تحسين القسم الرئيسي */
    .hero {
        padding: 120px 15px 60px;
        min-height: 80vh;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-text h1 {
        font-size: 2.2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }
    
    .hero-text h2 {
        font-size: 1.4rem;
        margin-bottom: 1rem;
    }
    
    .hero-text p {
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-buttons {
        justify-content: center;
        gap: 1rem;
    }
    
    .candidate-photo {
        max-width: 300px;
        border-radius: 15px;
    }
    
    /* تحسين الأزرار */
    .btn {
        padding: 14px 25px;
        font-size: 1rem;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* تحسين قسم الرؤية */
    .vision {
        padding: 60px 15px;
    }
    
    .vision h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }
    
    .vision-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .vision-card {
        padding: 1.5rem;
    }
    
    .vision-card i {
        font-size: 2.5rem;
    }
    
    .vision-card h3 {
        font-size: 1.3rem;
    }
    
    /* تحسين الإحصائيات */
    .stats {
        padding: 50px 15px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
    
    .stat-label {
        font-size: 1rem;
    }
    
    /* تحسين قسم الأخبار */
    .news {
        padding: 60px 15px;
    }
    
    .news h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .news-card img {
        height: 180px;
    }
    
    .news-content {
        padding: 1.2rem;
    }
    
    /* تحسين الفوتر */
    .footer {
        padding: 40px 15px 15px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .contact-info p {
        justify-content: center;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    /* تحسين الحاوي */
    .container {
        padding: 0 10px;
    }
    
    /* تحسين الهيدر */
    .nav-container {
        padding: 0 10px;
    }
    
    .logo h2 {
        font-size: 1rem;
    }
    
    .logo p {
        font-size: 0.7rem;
    }
    
    /* تحسين القسم الرئيسي */
    .hero {
        padding: 100px 10px 40px;
    }
    
    .hero-text h1 {
        font-size: 1.8rem;
    }
    
    .hero-text h2 {
        font-size: 1.2rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }
    
    .btn {
        width: 100%;
        max-width: 280px;
        padding: 16px 20px;
        font-size: 1rem;
    }
    
    .candidate-photo {
        max-width: 250px;
    }
    
    /* تحسين الإحصائيات */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stat-item {
        padding: 1.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
    }
    
    .stat-number {
        font-size: 2.2rem;
    }
    
    /* تحسين الكروت */
    .vision-card, .news-card {
        margin-bottom: 1rem;
    }
    
    .vision-card {
        padding: 1.2rem;
    }
    
    .vision-card i {
        font-size: 2rem;
    }
    
    .vision-card h3 {
        font-size: 1.2rem;
    }
    
    /* تحسين النصوص */
    .vision h2, .news h2 {
        font-size: 1.8rem;
    }
    
    /* تحسين المسافات */
    .vision, .news {
        padding: 40px 10px;
    }
    
    .stats {
        padding: 40px 10px;
    }
    
    .footer {
        padding: 30px 10px 10px;
    }
}

/* تحسينات للشاشات الصغيرة جداً (أقل من 360px) */
@media (max-width: 360px) {
    .hero-text h1 {
        font-size: 1.6rem;
    }
    
    .hero-text h2 {
        font-size: 1.1rem;
    }
    
    .btn {
        padding: 14px 15px;
        font-size: 0.9rem;
    }
    
    .candidate-photo {
        max-width: 220px;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .vision-card i {
        font-size: 1.8rem;
    }
    
    .vision h2, .news h2 {
        font-size: 1.6rem;
    }
}

/* تحسينات للمناظر الأفقية على الهواتف */
@media (max-width: 768px) and (orientation: landscape) {
    .hero {
        padding: 100px 15px 40px;
        min-height: 60vh;
    }
    
    .hero-content {
        gap: 1.5rem;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .hero-text h2 {
        font-size: 1.3rem;
    }
    
    .candidate-photo {
        max-width: 250px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* تحسينات إضافية للمس والتفاعل */
@media (max-width: 768px) {
    /* تحسين مناطق اللمس */
    .btn, .nav-menu a, .social-links a {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* تحسين التمرير */
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    /* تحسين التركيز */
    .btn:focus, input:focus, textarea:focus, select:focus {
        outline: 2px solid var(--secondary-color);
        outline-offset: 2px;
    }
    
    /* تحسين الهوفر للمس */
    .vision-card:active, .news-card:active, .btn:active {
        transform: scale(0.98);
    }
    
    /* إخفاء تأثيرات الهوفر على الأجهزة اللمسية */
    @media (hover: none) {
        .vision-card:hover, .news-card:hover, .btn:hover {
            transform: none;
        }
        
        .stat-number:hover {
            animation: none;
        }
    }
}

/* تأثيرات إضافية */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* أنيميشن للعداد */
@keyframes countUp {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.stat-number.animated {
    animation: countUp 0.8s ease-out;
}

/* تحسينات التصميم والتفاعلية */

/* تأثير الهوفر المحسن للأزرار */
.btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

/* تأثير الهوفر للكروت */
.vision-card, .news-card, .quality-card {
    position: relative;
    overflow: hidden;
}

.vision-card::before, .news-card::before, .quality-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(56, 161, 105, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.vision-card:hover::before, .news-card:hover::before, .quality-card:hover::before {
    opacity: 1;
    transform: rotate(45deg) translate(50%, 50%);
}

/* تأثير النبض للإحصائيات */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.stat-number:hover {
    animation: pulse 1s infinite;
    color: var(--light-color);
}

/* تحسين الهيدر مع تأثير الشفافية */
.header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

/* تأثير الموجة للقسم الرئيسي */
.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%23ffffff'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%23ffffff'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%23ffffff'%3E%3C/path%3E%3C/svg%3E") no-repeat;
    background-size: cover;
    z-index: 1;
}

/* تأثير التدرج المتحرك */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero {
    background: linear-gradient(-45deg, var(--background-light), #e8f5e8, #f0f8ff, var(--background-light));
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    position: relative;
}

/* تحسين صورة المرشح */
.candidate-photo {
    transition: all 0.3s ease;
    filter: brightness(1.1) contrast(1.1);
}

.candidate-photo:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    filter: brightness(1.2) contrast(1.2);
}

/* تأثير الكتابة المتحركة */
@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    from, to { border-color: transparent; }
    50% { border-color: var(--secondary-color); }
}

.hero-text h1 {
    overflow: hidden;
    border-left: 3px solid var(--secondary-color);
    white-space: nowrap;
    margin: 0 auto;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

/* تحسين الأيقونات */
.vision-card i, .quality-card i {
    transition: all 0.3s ease;
}

.vision-card:hover i, .quality-card:hover i {
    transform: rotateY(360deg) scale(1.2);
    color: var(--primary-color);
}

/* تأثير الجسيمات المتحركة */
.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(56, 161, 105, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(26, 54, 93, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(45, 90, 160, 0.1) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: 0;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(-10px) rotate(240deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
}

/* تحسين النماذج */
.form-group input, .form-group select, .form-group textarea {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 12px 15px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: var(--light-color);
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
    transform: translateY(-2px);
}

/* تأثير الريبل للأزرار */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

/* تحسين الفوتر */
.footer {
    background: linear-gradient(135deg, var(--text-dark) 0%, #1a202c 100%);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color), var(--accent-color));
}

/* تأثير التحميل */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* تحسين رسائل النجاح */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--secondary-color), #2f855a);
    color: var(--light-color);
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 10px;
}

.success-message.show {
    transform: translateX(0);
}

.success-message i {
    font-size: 1.2rem;
}

/* تحسين Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    animation: fadeInLightbox 0.3s ease forwards;
}

@keyframes fadeInLightbox {
    to { opacity: 1; }
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    animation: zoomIn 0.3s ease;
}

@keyframes zoomIn {
    from { transform: scale(0.5); }
    to { transform: scale(1); }
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: var(--light-color);
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.lightbox-close:hover {
    color: var(--secondary-color);
    transform: scale(1.2);
}

.lightbox img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.5);
}

.lightbox-caption {
    text-align: center;
    color: var(--light-color);
    margin-top: 15px;
    font-size: 1.1rem;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .hero::after {
        height: 50px;
    }
    
    .success-message {
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
    }
    
    .success-message.show {
        transform: translateY(0);
    }
}
